<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechManWorkflow-JointJS</title>
    <link rel="stylesheet" href="js/lib/jointjs-3.7.7.min.css">
    <style>
        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            width: 100%;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 主画布容器样式 */
        #paper-container {
            position: absolute;
            top: 0;
            left: 140px; /* Account for the left sidebar width */
            right: 0;
            bottom: 0;
            overflow: hidden;
            background-color: #f8f9fa;
        }

        /* 平移模式指示器样式 */
        .panning-mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 10000;
            display: none;
            pointer-events: none;
        }

        /* 鼠标指针样式 */
        .panning-cursor {
            cursor: grab !important;
        }
        .panning-cursor:active {
            cursor: grabbing !important;
        }

        /* 小地图样式 */
        #minimap-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 220px;
            height: 180px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            overflow: hidden;
            transition: opacity 0.3s ease;
        }

        #minimap-container:hover {
            opacity: 1 !important;
        }

        /* 小地图视口指示器 */
        .minimap-viewport {
            position: absolute;
            border: 2px solid rgba(25, 118, 210, 0.7);
            background-color: rgba(25, 118, 210, 0.1);
            z-index: 1001;
            cursor: move;
            pointer-events: all;
            transition: border-color 0.2s ease;
        }

        .minimap-viewport:hover {
            border-color: rgba(25, 118, 210, 0.9);
            background-color: rgba(25, 118, 210, 0.15);
        }

        /* 小地图标题 */
        .minimap-title {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background-color: rgba(240, 240, 240, 0.8);
            color: #666;
            font-size: 11px;
            padding: 2px 6px;
            text-align: center;
            border-bottom: 1px solid #ddd;
            z-index: 1002;
            pointer-events: none;
            user-select: none;
        }

        /* 缩放控制工具栏样式 */
        #zoom-toolbar {
            position: fixed;
            bottom: 20px;
            right: 250px; /* 位于小地图左侧 */
            background-color: rgba(255, 255, 255, 0.85);
            border: 1px solid #ccc;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 6px 12px;
            display: flex;
            align-items: center;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        #zoom-toolbar button {
            background: none;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            color: #555;
            transition: all 0.2s ease;
            outline: none;
            margin: 0 2px;
        }

        #zoom-toolbar button:hover {
            background-color: rgba(0, 0, 0, 0.1);
            transform: scale(1.1);
        }

        #zoom-toolbar button:active {
            background-color: rgba(0, 0, 0, 0.2);
            transform: scale(0.95);
        }

        #zoom-percentage {
            margin: 0 8px;
            font-size: 14px;
            font-weight: bold;
            color: #555;
            width: 50px;
            text-align: center;
            user-select: none;
            cursor: pointer;
            padding: 4px 6px;
            border-radius: 12px;
            transition: all 0.2s ease;
        }

        #zoom-percentage:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        #zoom-percentage:active {
            background-color: rgba(0, 0, 0, 0.2);
            transform: scale(0.95);
        }

        #toggle-minimap {
            margin-left: 8px;
            border-left: 1px solid #ddd;
            padding-left: 8px;
        }

        /* SVG图标样式 */
        .toolbar-icon {
            width: 18px;
            height: 18px;
            fill: #555;
            transition: fill 0.2s ease;
        }

        button:hover .toolbar-icon {
            fill: #333;
        }

        /* 小屏幕适配 */
        @media (max-width: 768px) {
            #zoom-toolbar {
                right: 210px;
                padding: 4px 8px;
            }

            #zoom-toolbar button {
                width: 26px;
                height: 26px;
            }

            #zoom-percentage {
                font-size: 12px;
                width: 40px;
            }
        }

        /* 加载指示器 */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10001;
            text-align: center;
            font-size: 16px;
            color: #333;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 主画布容器 -->
    <div id="paper-container"></div>

    <!-- 平移模式指示器 -->
    <div class="panning-mode-indicator">画布平移模式</div>

    <!-- 小地图容器 -->
    <div id="minimap-container"></div>

    <!-- 缩放控制工具栏 -->
    <div id="zoom-toolbar">
        <button id="zoom-out" type="button" title="缩小 (Ctrl+-)">
            <svg class="toolbar-icon" viewBox="0 0 24 24">
                <path d="M19,13H5V11H19V13Z" />
            </svg>
        </button>
        <div id="zoom-percentage" title="点击重置缩放 (Ctrl+0)">100%</div>
        <button id="zoom-in" type="button" title="放大 (Ctrl++)">
            <svg class="toolbar-icon" viewBox="0 0 24 24">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
            </svg>
        </button>
        <button id="toggle-minimap" type="button" title="切换小地图显示 (Ctrl+M)">
            <svg class="toolbar-icon" viewBox="0 0 24 24">
                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M10,18H6V12H10V18M18,18H12V12H18V18M18,10H6V6H18V10Z" />
            </svg>
        </button>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-indicator" id="loading-indicator">
        <div class="loading-spinner"></div>
        正在初始化工作流设计器...
    </div>

    <!-- JointJS依赖库 (本地化) -->
    <script src="js/lib/jquery-3.7.1.min.js"></script>
    <script src="js/lib/underscore-1.13.6.min.js"></script>
    <script src="js/lib/backbone-1.4.1.min.js"></script>
    <script src="js/lib/jointjs-3.7.7.min.js"></script>

    <!-- 应用模块 -->
    <script src="js/core/constants.js"></script>
    <script src="js/utils/helpers.js"></script>
    <script src="js/core/graph.js"></script>
    <script src="js/features/node-manager.js"></script>
    <script src="js/components/sidebar.js"></script>
    <script src="js/components/minimap.js"></script>
    <script src="js/components/zoom-toolbar.js"></script>
    <script src="js/components/property-panel.js"></script>

    <!-- 主应用入口 -->
    <script src="js/main.js"></script>

    <script>
        // 应用加载完成后隐藏加载指示器
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.opacity = '0';
                    loadingIndicator.style.transition = 'opacity 0.5s ease';
                    setTimeout(() => {
                        loadingIndicator.style.display = 'none';
                    }, 500);
                }
            }, 1000);
        });
    </script>
</body>
</html>
